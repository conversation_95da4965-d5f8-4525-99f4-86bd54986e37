# PayPal Configuration Database Schema

## 📊 Database Design

### 1. PayPalConfigs Table

```sql
CREATE TABLE PayPalConfigs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  
  -- Identification
  port VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique identifier for each config (e.g., "002", "local", "dth")',
  domain VARCHAR(255) NOT NULL COMMENT 'Associated domain name',
  
  -- Primary PayPal Account
  client_id VARCHAR(255) NOT NULL COMMENT 'PayPal Client ID',
  secret_key VARCHAR(500) NOT NULL COMMENT 'Encrypted PayPal Secret Key',
  
  -- Secondary PayPal Account (for backup/failover)
  client_id_2 VARCHAR(255) NULL COMMENT 'Secondary PayPal Client ID',
  secret_key_2 VARCHAR(500) NULL COMMENT 'Encrypted Secondary PayPal Secret Key',
  
  -- Configuration Settings
  email VARCHAR(255) NOT NULL COMMENT 'PayPal account email',
  hidden_product_name BOOLEAN DEFAULT FALSE COMMENT 'Whether to hide product names in PayPal',
  currency_code VARCHAR(3) DEFAULT 'USD' COMMENT 'Default currency code',
  
  -- Environment & Status
  environment ENUM('sandbox', 'production') DEFAULT 'production' COMMENT 'PayPal environment',
  is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether this config is active',
  
  -- Rate Limiting & Performance
  max_requests_per_minute INT DEFAULT 100 COMMENT 'Rate limit for this config',
  timeout_seconds INT DEFAULT 30 COMMENT 'Request timeout in seconds',
  
  -- Metadata
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(100) NULL COMMENT 'User who created this config',
  updated_by VARCHAR(100) NULL COMMENT 'User who last updated this config',
  
  -- Additional metadata
  notes TEXT NULL COMMENT 'Additional notes about this configuration',
  tags JSON NULL COMMENT 'Tags for categorization and filtering',
  
  -- Indexes for performance
  INDEX idx_port (port),
  INDEX idx_domain (domain),
  INDEX idx_client_id (client_id),
  INDEX idx_client_id_2 (client_id_2),
  INDEX idx_active (is_active),
  INDEX idx_environment (environment),
  INDEX idx_created_at (created_at),
  INDEX idx_updated_at (updated_at),
  
  -- Composite indexes
  INDEX idx_active_environment (is_active, environment),
  INDEX idx_domain_active (domain, is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. PayPalConfigAudits Table

```sql
CREATE TABLE PayPalConfigAudits (
  id INT PRIMARY KEY AUTO_INCREMENT,
  
  -- Reference to the config
  config_id INT NOT NULL COMMENT 'Reference to PayPalConfigs.id',
  
  -- Audit information
  action ENUM('CREATE', 'UPDATE', 'DELETE', 'ACTIVATE', 'DEACTIVATE', 'ENCRYPT', 'DECRYPT') NOT NULL,
  
  -- Change tracking
  old_values JSON NULL COMMENT 'Previous values (sensitive data excluded)',
  new_values JSON NULL COMMENT 'New values (sensitive data excluded)',
  changed_fields JSON NULL COMMENT 'List of fields that were changed',
  
  -- User information
  changed_by VARCHAR(100) NOT NULL COMMENT 'User who made the change',
  changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Request metadata
  ip_address VARCHAR(45) NULL COMMENT 'IP address of the user',
  user_agent TEXT NULL COMMENT 'User agent string',
  request_id VARCHAR(100) NULL COMMENT 'Request ID for tracing',
  
  -- Additional context
  reason TEXT NULL COMMENT 'Reason for the change',
  approval_status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'APPROVED',
  approved_by VARCHAR(100) NULL COMMENT 'User who approved the change',
  approved_at TIMESTAMP NULL,
  
  -- Foreign key constraint
  FOREIGN KEY (config_id) REFERENCES PayPalConfigs(id) ON DELETE CASCADE,
  
  -- Indexes
  INDEX idx_config_id (config_id),
  INDEX idx_action (action),
  INDEX idx_changed_at (changed_at),
  INDEX idx_changed_by (changed_by),
  INDEX idx_approval_status (approval_status),
  
  -- Composite indexes
  INDEX idx_config_action (config_id, action),
  INDEX idx_changed_by_date (changed_by, changed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3. PayPalConfigVersions Table (Optional - for versioning)

```sql
CREATE TABLE PayPalConfigVersions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  
  -- Reference
  config_id INT NOT NULL,
  version_number INT NOT NULL,
  
  -- Snapshot of config at this version
  config_snapshot JSON NOT NULL COMMENT 'Complete config data at this version',
  
  -- Version metadata
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(100) NOT NULL,
  change_summary TEXT NULL COMMENT 'Summary of changes in this version',
  
  -- Constraints
  FOREIGN KEY (config_id) REFERENCES PayPalConfigs(id) ON DELETE CASCADE,
  UNIQUE KEY unique_config_version (config_id, version_number),
  
  -- Indexes
  INDEX idx_config_id (config_id),
  INDEX idx_version_number (version_number),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 🔄 Migration Scripts

### 1. Create Tables Migration

```javascript
// migrations/20241201000001-create-paypal-configs.cjs
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create PayPalConfigs table
    await queryInterface.createTable('PayPalConfigs', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      port: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true
      },
      domain: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      client_id: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      secret_key: {
        type: Sequelize.STRING(500),
        allowNull: false
      },
      client_id_2: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      secret_key_2: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      hidden_product_name: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      currency_code: {
        type: Sequelize.STRING(3),
        defaultValue: 'USD'
      },
      environment: {
        type: Sequelize.ENUM('sandbox', 'production'),
        defaultValue: 'production'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      max_requests_per_minute: {
        type: Sequelize.INTEGER,
        defaultValue: 100
      },
      timeout_seconds: {
        type: Sequelize.INTEGER,
        defaultValue: 30
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      },
      created_by: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      updated_by: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('PayPalConfigs', ['port'], { name: 'idx_port' });
    await queryInterface.addIndex('PayPalConfigs', ['domain'], { name: 'idx_domain' });
    await queryInterface.addIndex('PayPalConfigs', ['client_id'], { name: 'idx_client_id' });
    await queryInterface.addIndex('PayPalConfigs', ['client_id_2'], { name: 'idx_client_id_2' });
    await queryInterface.addIndex('PayPalConfigs', ['is_active'], { name: 'idx_active' });
    await queryInterface.addIndex('PayPalConfigs', ['environment'], { name: 'idx_environment' });
    await queryInterface.addIndex('PayPalConfigs', ['is_active', 'environment'], { name: 'idx_active_environment' });
    await queryInterface.addIndex('PayPalConfigs', ['domain', 'is_active'], { name: 'idx_domain_active' });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('PayPalConfigs');
  }
};
```

### 2. Create Audit Table Migration

```javascript
// migrations/20241201000002-create-paypal-config-audits.cjs
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('PayPalConfigAudits', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      config_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'PayPalConfigs',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      action: {
        type: Sequelize.ENUM('CREATE', 'UPDATE', 'DELETE', 'ACTIVATE', 'DEACTIVATE', 'ENCRYPT', 'DECRYPT'),
        allowNull: false
      },
      old_values: {
        type: Sequelize.JSON,
        allowNull: true
      },
      new_values: {
        type: Sequelize.JSON,
        allowNull: true
      },
      changed_fields: {
        type: Sequelize.JSON,
        allowNull: true
      },
      changed_by: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      changed_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      request_id: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      reason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      approval_status: {
        type: Sequelize.ENUM('PENDING', 'APPROVED', 'REJECTED'),
        defaultValue: 'APPROVED'
      },
      approved_by: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      approved_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('PayPalConfigAudits', ['config_id'], { name: 'idx_config_id' });
    await queryInterface.addIndex('PayPalConfigAudits', ['action'], { name: 'idx_action' });
    await queryInterface.addIndex('PayPalConfigAudits', ['changed_at'], { name: 'idx_changed_at' });
    await queryInterface.addIndex('PayPalConfigAudits', ['changed_by'], { name: 'idx_changed_by' });
    await queryInterface.addIndex('PayPalConfigAudits', ['config_id', 'action'], { name: 'idx_config_action' });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('PayPalConfigAudits');
  }
};
```

### 3. Data Migration Script

```javascript
// migrations/20241201000003-migrate-paypal-json-data.cjs
'use strict';

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

module.exports = {
  async up(queryInterface, Sequelize) {
    // Read the existing paypal.json file
    const paypalJsonPath = path.join(__dirname, '../config/paypal.json');
    
    if (!fs.existsSync(paypalJsonPath)) {
      console.log('paypal.json not found, skipping data migration');
      return;
    }

    const paypalData = JSON.parse(fs.readFileSync(paypalJsonPath, 'utf8'));
    const encryptionKey = process.env.PAYPAL_ENCRYPTION_KEY;
    
    if (!encryptionKey) {
      throw new Error('PAYPAL_ENCRYPTION_KEY environment variable is required for migration');
    }

    // Encryption function
    function encrypt(text) {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher('aes-256-gcm', encryptionKey);
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return JSON.stringify({
        iv: iv.toString('hex'),
        data: encrypted,
        authTag: authTag.toString('hex')
      });
    }

    // Migrate each config
    for (const [port, config] of Object.entries(paypalData)) {
      try {
        await queryInterface.bulkInsert('PayPalConfigs', [{
          port,
          domain: config.email.includes('@') ? config.email.split('@')[1] : port,
          client_id: config.client_id,
          secret_key: encrypt(config.secret_key),
          client_id_2: config.client_id_2 || null,
          secret_key_2: config.secret_key_2 ? encrypt(config.secret_key_2) : null,
          email: config.email,
          hidden_product_name: config.hidden_product_name || false,
          currency_code: config.currency_code || 'USD',
          environment: port === 'local' ? 'sandbox' : 'production',
          is_active: true,
          created_by: 'migration_script',
          created_at: new Date(),
          updated_at: new Date()
        }]);

        console.log(`✅ Migrated config for port: ${port}`);
      } catch (error) {
        console.error(`❌ Error migrating port ${port}:`, error.message);
      }
    }

    console.log('PayPal configuration migration completed');
  },

  async down(queryInterface, Sequelize) {
    // Remove all migrated data
    await queryInterface.bulkDelete('PayPalConfigs', {
      created_by: 'migration_script'
    });
  }
};
```

## 🔍 Data Validation Queries

### Check Migration Success
```sql
-- Count total configs
SELECT COUNT(*) as total_configs FROM PayPalConfigs;

-- Check active configs by environment
SELECT environment, COUNT(*) as count 
FROM PayPalConfigs 
WHERE is_active = true 
GROUP BY environment;

-- Verify all ports are unique
SELECT port, COUNT(*) as count 
FROM PayPalConfigs 
GROUP BY port 
HAVING count > 1;

-- Check for missing required fields
SELECT id, port, domain 
FROM PayPalConfigs 
WHERE client_id IS NULL 
   OR secret_key IS NULL 
   OR email IS NULL;
```

### Performance Monitoring Queries
```sql
-- Most frequently accessed configs (based on audit logs)
SELECT pc.port, pc.domain, COUNT(pca.id) as access_count
FROM PayPalConfigs pc
LEFT JOIN PayPalConfigAudits pca ON pc.id = pca.config_id
WHERE pca.changed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY pc.id, pc.port, pc.domain
ORDER BY access_count DESC
LIMIT 10;

-- Recent configuration changes
SELECT pc.port, pca.action, pca.changed_by, pca.changed_at
FROM PayPalConfigAudits pca
JOIN PayPalConfigs pc ON pca.config_id = pc.id
WHERE pca.changed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY pca.changed_at DESC;
```

---

*Schema này được thiết kế để đảm bảo hiệu suất, bảo mật và khả năng mở rộng cho hệ thống PayPal configuration.*
